{"version": 3, "sources": ["../../@mui/utils/esm/elementTypeAcceptingRef/elementTypeAcceptingRef.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport chainPropTypes from \"../chainPropTypes/index.js\";\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction elementTypeAcceptingRef(props, propName, componentName, location, propFullName) {\n  const propValue = props[propName];\n  const safePropName = propFullName || propName;\n  if (propValue == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof propValue === 'function' && !isClassComponent(propValue)) {\n    warningHint = 'Did you accidentally provide a plain function component instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an element type that can hold a ref. ${warningHint} ` + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nexport default chainPropTypes(PropTypes.elementType, elementTypeAcceptingRef);"], "mappings": ";;;;;;;;;;;AAAA,wBAAsB;AAEtB,SAAS,iBAAiB,aAAa;AAErC,QAAM;AAAA,IACJ,YAAY,CAAC;AAAA,EACf,IAAI;AACJ,SAAO,QAAQ,UAAU,gBAAgB;AAC3C;AACA,SAAS,wBAAwB,OAAO,UAAU,eAAe,UAAU,cAAc;AACvF,QAAM,YAAY,MAAM,QAAQ;AAChC,QAAM,eAAe,gBAAgB;AACrC,MAAI,aAAa;AAAA;AAAA;AAAA;AAAA,EAKjB,OAAO,WAAW,aAAa;AAC7B,WAAO;AAAA,EACT;AACA,MAAI;AAWJ,MAAI,OAAO,cAAc,cAAc,CAAC,iBAAiB,SAAS,GAAG;AACnE,kBAAc;AAAA,EAChB;AACA,MAAI,gBAAgB,QAAW;AAC7B,WAAO,IAAI,MAAM,WAAW,QAAQ,MAAM,YAAY,oBAAoB,aAAa,qDAA0D,WAAW,oEAAyE;AAAA,EACvO;AACA,SAAO;AACT;AACA,IAAO,kCAAQ,eAAe,kBAAAA,QAAU,aAAa,uBAAuB;", "names": ["PropTypes"]}