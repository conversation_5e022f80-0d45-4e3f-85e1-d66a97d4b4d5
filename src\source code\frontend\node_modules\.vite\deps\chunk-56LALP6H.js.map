{"version": 3, "sources": ["../../inline-style-parser/index.js", "../../style-to-object/src/index.ts", "../../style-to-js/src/utilities.ts", "../../style-to-js/src/index.ts"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", "import type { Declaration } from 'inline-style-parser';\nimport parse from 'inline-style-parser';\n\nexport { Declaration };\n\ninterface StyleObject {\n  [name: string]: string;\n}\n\ntype Iterator = (\n  property: string,\n  value: string,\n  declaration: Declaration,\n) => void;\n\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nexport default function StyleToObject(\n  style: string,\n  iterator?: Iterator,\n): StyleObject | null {\n  let styleObject: StyleObject | null = null;\n\n  if (!style || typeof style !== 'string') {\n    return styleObject;\n  }\n\n  const declarations = parse(style);\n  const hasIterator = typeof iterator === 'function';\n\n  declarations.forEach((declaration) => {\n    if (declaration.type !== 'declaration') {\n      return;\n    }\n\n    const { property, value } = declaration;\n\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      styleObject = styleObject || {};\n      styleObject[property] = value;\n    }\n  });\n\n  return styleObject;\n}\n", "const CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nconst HYPHEN_REGEX = /-([a-z])/g;\nconst NO_HYPHEN_REGEX = /^[^-]+$/;\nconst VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nconst MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n\n/**\n * Checks whether to skip camelCase.\n */\nconst skipCamelCase = (property: string) =>\n  !property ||\n  NO_HYPHEN_REGEX.test(property) ||\n  CUSTOM_PROPERTY_REGEX.test(property);\n\n/**\n * Replacer that capitalizes first character.\n */\nconst capitalize = (match: string, character: string) =>\n  character.toUpperCase();\n\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nconst trimHyphen = (match: string, prefix: string) => `${prefix}-`;\n\n/**\n * CamelCase options.\n */\nexport interface CamelCaseOptions {\n  reactCompat?: boolean;\n}\n\n/**\n * CamelCases a CSS property.\n */\nexport const camelCase = (property: string, options: CamelCaseOptions = {}) => {\n  if (skipCamelCase(property)) {\n    return property;\n  }\n\n  property = property.toLowerCase();\n\n  if (options.reactCompat) {\n    // `-ms` vendor prefix should not be capitalized\n    property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n  } else {\n    // for non-React, remove first hyphen so vendor prefix is not capitalized\n    property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n  }\n\n  return property.replace(HYPHEN_REGEX, capitalize);\n};\n", "import StyleToObject from 'style-to-object';\n\nimport { camelCase, CamelCaseOptions } from './utilities';\n\ntype StyleObject = Record<string, string>;\n\ninterface StyleToJSOptions extends CamelCaseOptions {}\n\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style: string, options?: StyleToJSOptions): StyleObject {\n  const output: StyleObject = {};\n\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n\n  StyleToObject(style, (property, value) => {\n    // skip CSS comment\n    if (property && value) {\n      output[camelCase(property, options)] = value;\n    }\n  });\n\n  return output;\n}\n\nStyleToJS.default = StyleToJS;\n\nexport = StyleToJS;\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAEpB,QAAI,gBAAgB;AACpB,QAAI,mBAAmB;AAGvB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AAGtB,QAAI,aAAa;AAGjB,QAAI,UAAU;AACd,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACf,QAAI,eAAe;AAGnB,QAAI,eAAe;AACnB,QAAI,mBAAmB;AASvB,WAAO,UAAU,SAAU,OAAO,SAAS;AACzC,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,CAAC,MAAO,QAAO,CAAC;AAEpB,gBAAU,WAAW,CAAC;AAKtB,UAAI,SAAS;AACb,UAAI,SAAS;AAOb,eAAS,eAAe,KAAK;AAC3B,YAAI,QAAQ,IAAI,MAAM,aAAa;AACnC,YAAI,MAAO,WAAU,MAAM;AAC3B,YAAI,IAAI,IAAI,YAAY,OAAO;AAC/B,iBAAS,CAAC,IAAI,IAAI,SAAS,IAAI,SAAS,IAAI;AAAA,MAC9C;AAOA,eAAS,WAAW;AAClB,YAAI,QAAQ,EAAE,MAAM,QAAQ,OAAe;AAC3C,eAAO,SAAU,MAAM;AACrB,eAAK,WAAW,IAAI,SAAS,KAAK;AAClC,qBAAW;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAUA,eAAS,SAAS,OAAO;AACvB,aAAK,QAAQ;AACb,aAAK,MAAM,EAAE,MAAM,QAAQ,OAAe;AAC1C,aAAK,SAAS,QAAQ;AAAA,MACxB;AAKA,eAAS,UAAU,UAAU;AAE7B,UAAI,aAAa,CAAC;AAQlB,eAAS,MAAM,KAAK;AAClB,YAAI,MAAM,IAAI;AAAA,UACZ,QAAQ,SAAS,MAAM,SAAS,MAAM,SAAS,OAAO;AAAA,QACxD;AACA,YAAI,SAAS;AACb,YAAI,WAAW,QAAQ;AACvB,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,SAAS;AAEb,YAAI,QAAQ,QAAQ;AAClB,qBAAW,KAAK,GAAG;AAAA,QACrB,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAQA,eAAS,MAAM,IAAI;AACjB,YAAI,IAAI,GAAG,KAAK,KAAK;AACrB,YAAI,CAAC,EAAG;AACR,YAAI,MAAM,EAAE,CAAC;AACb,uBAAe,GAAG;AAClB,gBAAQ,MAAM,MAAM,IAAI,MAAM;AAC9B,eAAO;AAAA,MACT;AAKA,eAAS,aAAa;AACpB,cAAM,gBAAgB;AAAA,MACxB;AAQA,eAAS,SAAS,OAAO;AACvB,YAAI;AACJ,gBAAQ,SAAS,CAAC;AAClB,eAAQ,IAAI,QAAQ,GAAI;AACtB,cAAI,MAAM,OAAO;AACf,kBAAM,KAAK,CAAC;AAAA,UACd;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAQA,eAAS,UAAU;AACjB,YAAI,MAAM,SAAS;AACnB,YAAI,iBAAiB,MAAM,OAAO,CAAC,KAAK,YAAY,MAAM,OAAO,CAAC,EAAG;AAErE,YAAI,IAAI;AACR,eACE,gBAAgB,MAAM,OAAO,CAAC,MAC7B,YAAY,MAAM,OAAO,CAAC,KAAK,iBAAiB,MAAM,OAAO,IAAI,CAAC,IACnE;AACA,YAAE;AAAA,QACJ;AACA,aAAK;AAEL,YAAI,iBAAiB,MAAM,OAAO,IAAI,CAAC,GAAG;AACxC,iBAAO,MAAM,wBAAwB;AAAA,QACvC;AAEA,YAAI,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC;AAC9B,kBAAU;AACV,uBAAe,GAAG;AAClB,gBAAQ,MAAM,MAAM,CAAC;AACrB,kBAAU;AAEV,eAAO,IAAI;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAQA,eAAS,cAAc;AACrB,YAAI,MAAM,SAAS;AAGnB,YAAI,OAAO,MAAM,cAAc;AAC/B,YAAI,CAAC,KAAM;AACX,gBAAQ;AAGR,YAAI,CAAC,MAAM,WAAW,EAAG,QAAO,MAAM,sBAAsB;AAG5D,YAAI,MAAM,MAAM,WAAW;AAE3B,YAAI,MAAM,IAAI;AAAA,UACZ,MAAM;AAAA,UACN,UAAU,KAAK,KAAK,CAAC,EAAE,QAAQ,eAAe,YAAY,CAAC;AAAA,UAC3D,OAAO,MACH,KAAK,IAAI,CAAC,EAAE,QAAQ,eAAe,YAAY,CAAC,IAChD;AAAA,QACN,CAAC;AAGD,cAAM,eAAe;AAErB,eAAO;AAAA,MACT;AAOA,eAAS,eAAe;AACtB,YAAI,QAAQ,CAAC;AAEb,iBAAS,KAAK;AAGd,YAAI;AACJ,eAAQ,OAAO,YAAY,GAAI;AAC7B,cAAI,SAAS,OAAO;AAClB,kBAAM,KAAK,IAAI;AACf,qBAAS,KAAK;AAAA,UAChB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,iBAAW;AACX,aAAO,aAAa;AAAA,IACtB;AAQA,aAAS,KAAK,KAAK;AACjB,aAAO,MAAM,IAAI,QAAQ,YAAY,YAAY,IAAI;AAAA,IACvD;AAAA;AAAA;;;;;;;;;;ACvOA,YAAA,UAAA;AA5BA,QAAA,wBAAA,gBAAA,6BAAA;AA4BA,aAAwB,cACtB,OACA,UAAmB;AAEnB,UAAI,cAAkC;AAEtC,UAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,eAAO;MACT;AAEA,UAAM,gBAAe,GAAA,sBAAA,SAAM,KAAK;AAChC,UAAM,cAAc,OAAO,aAAa;AAExC,mBAAa,QAAQ,SAAC,aAAW;AAC/B,YAAI,YAAY,SAAS,eAAe;AACtC;QACF;AAEQ,YAAA,WAAoB,YAAW,UAArB,QAAU,YAAW;AAEvC,YAAI,aAAa;AACf,mBAAS,UAAU,OAAO,WAAW;QACvC,WAAW,OAAO;AAChB,wBAAc,eAAe,CAAA;AAC7B,sBAAY,QAAQ,IAAI;QAC1B;MACF,CAAC;AAED,aAAO;IACT;;;;;;;;;;AC1DA,QAAM,wBAAwB;AAC9B,QAAM,eAAe;AACrB,QAAM,kBAAkB;AACxB,QAAM,sBAAsB;AAC5B,QAAM,yBAAyB;AAK/B,QAAM,gBAAgB,SAAC,UAAgB;AACrC,aAAA,CAAC,YACD,gBAAgB,KAAK,QAAQ,KAC7B,sBAAsB,KAAK,QAAQ;IAFnC;AAOF,QAAM,aAAa,SAAC,OAAe,WAAiB;AAClD,aAAA,UAAU,YAAW;IAArB;AAKF,QAAM,aAAa,SAAC,OAAe,QAAc;AAAK,aAAA,GAAA,OAAG,QAAM,GAAA;IAAT;AAY/C,QAAM,YAAY,SAAC,UAAkB,SAA8B;AAA9B,UAAA,YAAA,QAAA;AAAA,kBAAA,CAAA;MAA8B;AACxE,UAAI,cAAc,QAAQ,GAAG;AAC3B,eAAO;MACT;AAEA,iBAAW,SAAS,YAAW;AAE/B,UAAI,QAAQ,aAAa;AAEvB,mBAAW,SAAS,QAAQ,wBAAwB,UAAU;MAChE,OAAO;AAEL,mBAAW,SAAS,QAAQ,qBAAqB,UAAU;MAC7D;AAEA,aAAO,SAAS,QAAQ,cAAc,UAAU;IAClD;AAhBa,YAAA,YAAS;;;;;;;;;;;ACnCtB,QAAA,oBAAA,gBAAA,aAAA;AAEA,QAAA,cAAA;AASA,aAAS,UAAU,OAAe,SAA0B;AAC1D,UAAM,SAAsB,CAAA;AAE5B,UAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,eAAO;MACT;AAEA,OAAA,GAAA,kBAAA,SAAc,OAAO,SAAC,UAAU,OAAK;AAEnC,YAAI,YAAY,OAAO;AACrB,kBAAO,GAAA,YAAA,WAAU,UAAU,OAAO,CAAC,IAAI;QACzC;MACF,CAAC;AAED,aAAO;IACT;AAEA,cAAU,UAAU;AAEpB,WAAA,UAAS;;;", "names": []}