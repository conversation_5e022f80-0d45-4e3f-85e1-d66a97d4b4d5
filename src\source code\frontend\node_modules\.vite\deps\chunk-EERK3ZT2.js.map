{"version": 3, "sources": ["../../@mui/material/esm/Dialog/DialogContext.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst DialogContext = /*#__PURE__*/React.createContext({});\nif (process.env.NODE_ENV !== 'production') {\n  DialogContext.displayName = 'DialogContext';\n}\nexport default DialogContext;"], "mappings": ";;;;;;;;AAEA,YAAuB;AACvB,IAAM,gBAAmC,oBAAc,CAAC,CAAC;AACzD,IAAI,MAAuC;AACzC,gBAAc,cAAc;AAC9B;AACA,IAAO,wBAAQ;", "names": []}