{"version": 3, "sources": ["../../@mui/material/esm/AppBar/AppBar.js", "../../@mui/material/esm/AppBar/appBarClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getAppBarUtilityClass } from \"./appBarClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, `position${capitalize(position)}`]\n  };\n  return composeClasses(slots, getAppBarUtilityClass, classes);\n};\n\n// var2 is the fallback.\n// Ex. var1: 'var(--a)', var2: 'var(--b)'; return: 'var(--a, var(--b))'\nconst joinVars = (var1, var2) => var1 ? `${var1?.replace(')', '')}, ${var2})` : var2;\nconst AppBarRoot = styled(Paper, {\n  name: 'MuiAppBar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`position${capitalize(ownerState.position)}`], styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  flexDirection: 'column',\n  width: '100%',\n  boxSizing: 'border-box',\n  // Prevent padding issue with the Modal and fixed positioned AppBar.\n  flexShrink: 0,\n  variants: [{\n    props: {\n      position: 'fixed'\n    },\n    style: {\n      position: 'fixed',\n      zIndex: (theme.vars || theme).zIndex.appBar,\n      top: 0,\n      left: 'auto',\n      right: 0,\n      '@media print': {\n        // Prevent the app bar to be visible on each printed page.\n        position: 'absolute'\n      }\n    }\n  }, {\n    props: {\n      position: 'absolute'\n    },\n    style: {\n      position: 'absolute',\n      zIndex: (theme.vars || theme).zIndex.appBar,\n      top: 0,\n      left: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      position: 'sticky'\n    },\n    style: {\n      position: 'sticky',\n      zIndex: (theme.vars || theme).zIndex.appBar,\n      top: 0,\n      left: 'auto',\n      right: 0\n    }\n  }, {\n    props: {\n      position: 'static'\n    },\n    style: {\n      position: 'static'\n    }\n  }, {\n    props: {\n      position: 'relative'\n    },\n    style: {\n      position: 'relative'\n    }\n  }, {\n    props: {\n      color: 'inherit'\n    },\n    style: {\n      '--AppBar-color': 'inherit'\n    }\n  }, {\n    props: {\n      color: 'default'\n    },\n    style: {\n      '--AppBar-background': theme.vars ? theme.vars.palette.AppBar.defaultBg : theme.palette.grey[100],\n      '--AppBar-color': theme.vars ? theme.vars.palette.text.primary : theme.palette.getContrastText(theme.palette.grey[100]),\n      ...theme.applyStyles('dark', {\n        '--AppBar-background': theme.vars ? theme.vars.palette.AppBar.defaultBg : theme.palette.grey[900],\n        '--AppBar-color': theme.vars ? theme.vars.palette.text.primary : theme.palette.getContrastText(theme.palette.grey[900])\n      })\n    }\n  }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['contrastText'])).map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      '--AppBar-background': (theme.vars ?? theme).palette[color].main,\n      '--AppBar-color': (theme.vars ?? theme).palette[color].contrastText\n    }\n  })), {\n    props: props => props.enableColorOnDark === true && !['inherit', 'transparent'].includes(props.color),\n    style: {\n      backgroundColor: 'var(--AppBar-background)',\n      color: 'var(--AppBar-color)'\n    }\n  }, {\n    props: props => props.enableColorOnDark === false && !['inherit', 'transparent'].includes(props.color),\n    style: {\n      backgroundColor: 'var(--AppBar-background)',\n      color: 'var(--AppBar-color)',\n      ...theme.applyStyles('dark', {\n        backgroundColor: theme.vars ? joinVars(theme.vars.palette.AppBar.darkBg, 'var(--AppBar-background)') : null,\n        color: theme.vars ? joinVars(theme.vars.palette.AppBar.darkColor, 'var(--AppBar-color)') : null\n      })\n    }\n  }, {\n    props: {\n      color: 'transparent'\n    },\n    style: {\n      '--AppBar-background': 'transparent',\n      '--AppBar-color': 'inherit',\n      backgroundColor: 'var(--AppBar-background)',\n      color: 'var(--AppBar-color)',\n      ...theme.applyStyles('dark', {\n        backgroundImage: 'none'\n      })\n    }\n  }]\n})));\nconst AppBar = /*#__PURE__*/React.forwardRef(function AppBar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAppBar'\n  });\n  const {\n    className,\n    color = 'primary',\n    enableColorOnDark = false,\n    position = 'fixed',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    position,\n    enableColorOnDark\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AppBarRoot, {\n    square: true,\n    component: \"header\",\n    ownerState: ownerState,\n    elevation: 4,\n    className: clsx(classes.root, className, position === 'fixed' && 'mui-fixed'),\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AppBar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'inherit', 'primary', 'secondary', 'transparent', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * If true, the `color` prop is applied in dark mode.\n   * @default false\n   */\n  enableColorOnDark: PropTypes.bool,\n  /**\n   * The positioning type. The behavior of the different options is described\n   * [in the MDN web docs](https://developer.mozilla.org/en-US/docs/Web/CSS/position).\n   * Note: `sticky` is not universally supported and will fall back to `static` when unavailable.\n   * @default 'fixed'\n   */\n  position: PropTypes.oneOf(['absolute', 'fixed', 'relative', 'static', 'sticky']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AppBar;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAppBarUtilityClass(slot) {\n  return generateUtilityClass('MuiAppBar', slot);\n}\nconst appBarClasses = generateUtilityClasses('MuiAppBar', ['root', 'positionFixed', 'positionAbsolute', 'positionSticky', 'positionStatic', 'positionRelative', 'colorDefault', 'colorPrimary', 'colorSecondary', 'colorInherit', 'colorTransparent', 'colorError', 'colorInfo', 'colorSuccess', 'colorWarning']);\nexport default appBarClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACA,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,iBAAiB,oBAAoB,kBAAkB,kBAAkB,oBAAoB,gBAAgB,gBAAgB,kBAAkB,gBAAgB,oBAAoB,cAAc,aAAa,gBAAgB,cAAc,CAAC;AAChT,IAAO,wBAAQ;;;ADOf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,QAAQ,mBAAW,KAAK,CAAC,IAAI,WAAW,mBAAW,QAAQ,CAAC,EAAE;AAAA,EAC/E;AACA,SAAO,eAAe,OAAO,uBAAuB,OAAO;AAC7D;AAIA,IAAM,WAAW,CAAC,MAAM,SAAS,OAAO,GAAG,6BAAM,QAAQ,KAAK,GAAG,KAAK,IAAI,MAAM;AAChF,IAAM,aAAa,eAAO,eAAO;AAAA,EAC/B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,mBAAW,WAAW,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,mBAAW,WAAW,KAAK,CAAC,EAAE,CAAC;AAAA,EAC3H;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,eAAe;AAAA,EACf,OAAO;AAAA,EACP,WAAW;AAAA;AAAA,EAEX,YAAY;AAAA,EACZ,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,MACrC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,MACP,gBAAgB;AAAA;AAAA,QAEd,UAAU;AAAA,MACZ;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,MACrC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,MACV,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,MACrC,KAAK;AAAA,MACL,MAAM;AAAA,MACN,OAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,IACA,OAAO;AAAA,MACL,UAAU;AAAA,IACZ;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,kBAAkB;AAAA,IACpB;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,uBAAuB,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,QAAQ,KAAK,GAAG;AAAA,MAChG,kBAAkB,MAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,UAAU,MAAM,QAAQ,gBAAgB,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,MACtH,GAAG,MAAM,YAAY,QAAQ;AAAA,QAC3B,uBAAuB,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,QAAQ,KAAK,GAAG;AAAA,QAChG,kBAAkB,MAAM,OAAO,MAAM,KAAK,QAAQ,KAAK,UAAU,MAAM,QAAQ,gBAAgB,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,MACxH,CAAC;AAAA,IACH;AAAA,EACF,GAAG,GAAG,OAAO,QAAQ,MAAM,OAAO,EAAE,OAAO,+BAA+B,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,OAAO;AAAA,IAC7G,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,wBAAwB,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,MAC5D,mBAAmB,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,IACzD;AAAA,EACF,EAAE,GAAG;AAAA,IACH,OAAO,WAAS,MAAM,sBAAsB,QAAQ,CAAC,CAAC,WAAW,aAAa,EAAE,SAAS,MAAM,KAAK;AAAA,IACpG,OAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACT;AAAA,EACF,GAAG;AAAA,IACD,OAAO,WAAS,MAAM,sBAAsB,SAAS,CAAC,CAAC,WAAW,aAAa,EAAE,SAAS,MAAM,KAAK;AAAA,IACrG,OAAO;AAAA,MACL,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,GAAG,MAAM,YAAY,QAAQ;AAAA,QAC3B,iBAAiB,MAAM,OAAO,SAAS,MAAM,KAAK,QAAQ,OAAO,QAAQ,0BAA0B,IAAI;AAAA,QACvG,OAAO,MAAM,OAAO,SAAS,MAAM,KAAK,QAAQ,OAAO,WAAW,qBAAqB,IAAI;AAAA,MAC7F,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,OAAO;AAAA,IACT;AAAA,IACA,OAAO;AAAA,MACL,uBAAuB;AAAA,MACvB,kBAAkB;AAAA,MAClB,iBAAiB;AAAA,MACjB,OAAO;AAAA,MACP,GAAG,MAAM,YAAY,QAAQ;AAAA,QAC3B,iBAAiB;AAAA,MACnB,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,SAA4B,iBAAW,SAASA,QAAO,SAAS,KAAK;AACzE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,WAAW;AAAA,IACX,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,YAAY;AAAA,IACnC,QAAQ;AAAA,IACR,WAAW;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX,WAAW,aAAK,QAAQ,MAAM,WAAW,aAAa,WAAW,WAAW;AAAA,IAC5E;AAAA,IACA,GAAG;AAAA,EACL,CAAC;AACH,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,WAAW,aAAa,eAAe,SAAS,QAAQ,WAAW,SAAS,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1M,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B,UAAU,kBAAAA,QAAU,MAAM,CAAC,YAAY,SAAS,YAAY,UAAU,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA,EAI/E,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,iBAAQ;", "names": ["AppBar", "_jsx", "PropTypes"]}