{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google-cloud/dialogflow": "^7.1.0", "archiver": "^7.0.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dialogflow": "^1.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "google-auth-library": "^9.15.1", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.14.1", "mongoose-paginate-v2": "^1.9.1", "multer": "^2.0.1", "nodemailer": "^7.0.3", "quill-image-uploader": "^1.3.0", "quill-resize-image": "^1.0.7", "sharp": "^0.34.2", "socket.io": "^4.8.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}