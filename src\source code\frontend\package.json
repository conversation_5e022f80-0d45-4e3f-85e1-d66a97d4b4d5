{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@react-oauth/google": "^0.12.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^6.2.1", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "html-react-parser": "^5.2.5", "html-to-draftjs": "^1.5.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.507.0", "quill": "^2.0.3", "quill-image-resize-module-react": "^3.0.0", "quill-image-uploader": "^1.3.0", "react": "^18.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.0.0", "react-draft-wysiwyg": "^1.15.0", "react-easy-crop": "^5.4.2", "react-image-crop": "^11.0.10", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "slugify": "^1.6.6", "socket.io-client": "^4.8.1", "tinymce": "^7.9.1", "uuid": "^11.1.0", "web-vitals": "^2.1.4"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.5.0", "vite": "^6.3.5"}}