import React, { useState, useEffect } from 'react';
import {
    Container,
    Typography,
    Box,
    Paper,
    Grid,
    Card,
    CardContent,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    CircularProgress,
    Alert,
    Chip,
    List,
    ListItem,
    ListItemText,
    ListItemAvatar,
    Avatar,
    Divider,
    Tab,
    Tabs,
    Button,
    TextField
} from '@mui/material';
import {
    TrendingUp as TrendingUpIcon,
    People as PeopleIcon,
    Article as ArticleIcon,
    Topic as TopicIcon,
    Comment as CommentIcon,
    Favorite as FavoriteIcon,
    Search as SearchIcon,
    Star as StarIcon,
    Event as EventIcon,
    Refresh as RefreshIcon
} from '@mui/icons-material';
import {
    LineChart,
    Line,
    AreaChart,
    Area,
    BarChart,
    Bar,
    PieChart,
    Pie,
    Cell,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer
} from 'recharts';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip as ChartTooltip,
    Legend as ChartLegend,
    ArcElement,
    BarElement,
    RadialLinearScale
} from 'chart.js';
import { <PERSON>hnut, Radar, PolarArea } from 'react-chartjs-2';
import axios from 'axios';
import { DateRangePicker } from 'react-date-range';
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    ChartTooltip,
    ChartLegend,
    ArcElement,
    BarElement,
    RadialLinearScale
);

const AdminAnalyticsPage = () => {
    // State management
    const [loading, setLoading] = useState(false);
    const [selectedTab, setSelectedTab] = useState(0);
    const [timePeriod, setTimePeriod] = useState('last_month');
    const [overviewData, setOverviewData] = useState(null);
    const [userActivityData, setUserActivityData] = useState(null);
    const [popularContentData, setPopularContentData] = useState(null);
    const [searchAnalyticsData, setSearchAnalyticsData] = useState(null);
    const [communityData, setCommunityData] = useState(null);
    const [error, setError] = useState(null);
    const [customDateRange, setCustomDateRange] = useState([
        {
            startDate: new Date(),
            endDate: new Date(),
            key: 'selection'
        }
    ]);
    const [showDatePicker, setShowDatePicker] = useState(false);


    // API base URL
    const API_BASE_URL = 'http://localhost:5000/api';

    // Get auth token
    const getAuthToken = () => {
        const token = localStorage.getItem('token');
        if (token) return token;
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        return user.token;
    };

    // Colors for charts
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

    // Fetch overview data
    const fetchOverviewData = async () => {
        try {
            const token = getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/admin/analytics/overview`, {
                headers: { Authorization: `Bearer ${token}` },
                params: { days: 30 } // Default to 30 days for overview
            });

            if (response.data.success) {
                setOverviewData(response.data.data);
            }
        } catch (error) {
            console.error('Error fetching overview data:', error);
            setError('Lỗi khi tải dữ liệu tổng quan');
        }
    };

    // Fetch user activity data
    const fetchUserActivityData = async () => {
        try {
            const token = getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/admin/analytics/user-activity`, {
                headers: { Authorization: `Bearer ${token}` },
                params: { days: 30 }
            });

            if (response.data.success) {
                setUserActivityData(response.data.data);
            }
        } catch (error) {
            console.error('Error fetching user activity data:', error);
            setError('Lỗi khi tải dữ liệu hoạt động người dùng');
        }
    };

    // Fetch popular content data
    const fetchPopularContentData = async () => {
        try {
            const token = getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/admin/analytics/popular-content`, {
                headers: { Authorization: `Bearer ${token}` },
                params: { days: 30 }
            });

            if (response.data.success) {
                setPopularContentData(response.data.data);
            }
        } catch (error) {
            console.error('Error fetching popular content data:', error);
            setError('Lỗi khi tải dữ liệu nội dung phổ biến');
        }
    };

    // Fetch search analytics data
    const fetchSearchAnalyticsData = async () => {
        try {
            const token = getAuthToken();
            const response = await axios.get(`${API_BASE_URL}/admin/analytics/search-analytics`, {
                headers: { Authorization: `Bearer ${token}` },
                params: { days: 30 }
            });

            if (response.data.success) {
                setSearchAnalyticsData(response.data.data);
            }
        } catch (error) {
            console.error('Error fetching search analytics data:', error);
            setError('Lỗi khi tải dữ liệu phân tích tìm kiếm');
        }
    };

    // Fetch community stats data
    const fetchCommunityData = async () => {
        try {
            const token = getAuthToken();
            let params = { period: timePeriod };
            if (timePeriod === 'custom') {
                params.customStartDate = customDateRange[0].startDate.toISOString();
                params.customEndDate = customDateRange[0].endDate.toISOString();
            }

            const response = await axios.get(`${API_BASE_URL}/admin/analytics/community`, {
                headers: { Authorization: `Bearer ${token}` },
                params
            });

            if (response.data.success) {
                setCommunityData(response.data.data);
            }
        } catch (error) {
            console.error('Error fetching community data:', error);
            setError('Lỗi khi tải dữ liệu cộng đồng');
        }
    };


    // Fetch all data
    const fetchAllData = async () => {
        setLoading(true);
        setError(null);

        try {
            await Promise.all([
                fetchOverviewData(),
                fetchUserActivityData(),
                fetchPopularContentData(),
                fetchSearchAnalyticsData(),
                fetchCommunityData()
            ]);
        } catch (error) {
            console.error('Error fetching analytics data:', error);
            setError('Lỗi khi tải dữ liệu thống kê');
        } finally {
            setLoading(false);
        }
    };

    // Format number with K, M suffix
    const formatNumber = (num) => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    };

    // Format date for charts
    const formatDate = (dateStr) => {
        const date = new Date(dateStr);
        return date.toLocaleDateString('vi-VN', { month: 'short', day: 'numeric' });
    };

    // Handle tab change
    const handleTabChange = (event, newValue) => {
        setSelectedTab(newValue);
    };

    // Handle time period change
    const handleTimePeriodChange = (event) => {
        const newPeriod = event.target.value;
        setTimePeriod(newPeriod);
        if (newPeriod === 'custom') {
            setShowDatePicker(true);
        } else {
            setShowDatePicker(false);
        }
    };

    // Handle custom date range change
    const handleDateRangeChange = (ranges) => {
        setCustomDateRange([ranges.selection]);
    };

    // Effects
    useEffect(() => {
        fetchAllData();
    }, [timePeriod, customDateRange]);


    // Tab panel component
    const TabPanel = ({ children, value, index, ...other }) => (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`analytics-tabpanel-${index}`}
            aria-labelledby={`analytics-tab-${index}`}
            {...other}
        >
            {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
        </div>
    );

    if (loading && !overviewData) {
        return (
            <Container maxWidth="xl">
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
                    <CircularProgress />
                </Box>
            </Container>
        );
    }

    return (
        <Container maxWidth="xl">
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h4" gutterBottom>
                    Thống kê và Phân tích
                </Typography>

                <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                    <FormControl size="small" sx={{ minWidth: 150 }}>
                        <InputLabel>Thời gian</InputLabel>
                        <Select
                            value={timePeriod}
                            label="Thời gian"
                            onChange={handleTimePeriodChange}
                        >
                            <MenuItem value="yesterday">Hôm qua</MenuItem>
                            <MenuItem value="last_week">Tuần trước</MenuItem>
                            <MenuItem value="last_month">Tháng trước</MenuItem>
                            <MenuItem value="custom">Tùy chỉnh</MenuItem>
                        </Select>
                    </FormControl>

                    {timePeriod === 'custom' && (
                        <Button
                            variant="outlined"
                            startIcon={<EventIcon />}
                            onClick={() => setShowDatePicker(!showDatePicker)}
                        >
                            Chọn ngày
                        </Button>
                    )}

                    <Button
                        variant="outlined"
                        startIcon={<RefreshIcon />}
                        onClick={fetchAllData}
                        disabled={loading}
                    >
                        Làm mới
                    </Button>
                </Box>
            </Box>

            {timePeriod === 'custom' && showDatePicker && (
                <Paper sx={{ p: 2, mb: 3, position: 'absolute', zIndex: 10 }}>
                    <DateRangePicker
                        onChange={handleDateRangeChange}
                        showSelectionPreview={true}
                        moveRangeOnFirstSelection={false}
                        months={2}
                        ranges={customDateRange}
                        direction="horizontal"
                    />
                    <Button onClick={() => setShowDatePicker(false)} sx={{ mt: 1 }}>
                        Đóng
                    </Button>
                </Paper>
            )}


            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {/* Overview Stats Cards */}
            {overviewData && (
                <Grid container spacing={3} sx={{ mb: 3 }}>
                    <Grid item xs={12} sm={6} md={2.4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <PeopleIcon sx={{ mr: 2, color: 'primary.main', fontSize: 40 }} />
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom>
                                            Người dùng
                                        </Typography>
                                        <Typography variant="h5">
                                            {formatNumber(overviewData.totals.users)}
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={2.4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <ArticleIcon sx={{ mr: 2, color: 'info.main', fontSize: 40 }} />
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom>
                                            Bài viết
                                        </Typography>
                                        <Typography variant="h5">
                                            {formatNumber(overviewData.totals.posts)}
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={2.4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <TopicIcon sx={{ mr: 2, color: 'warning.main', fontSize: 40 }} />
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom>
                                            Chủ đề
                                        </Typography>
                                        <Typography variant="h5">
                                            {formatNumber(overviewData.totals.topics)}
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={2.4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <CommentIcon sx={{ mr: 2, color: 'secondary.main', fontSize: 40 }} />
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom>
                                            Bình luận
                                        </Typography>
                                        <Typography variant="h5">
                                            {formatNumber(overviewData.totals.comments)}
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>

                    <Grid item xs={12} sm={6} md={2.4}>
                        <Card>
                            <CardContent>
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <FavoriteIcon sx={{ mr: 2, color: 'error.main', fontSize: 40 }} />
                                    <Box>
                                        <Typography color="textSecondary" gutterBottom>
                                            Lượt thích
                                        </Typography>
                                        <Typography variant="h5">
                                            {formatNumber(overviewData.totals.likes)}
                                        </Typography>
                                    </Box>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>
                </Grid>
            )}

            {/* Tabs for different analytics sections */}
            <Paper sx={{ mb: 3 }}>
                <Tabs
                    value={selectedTab}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons="auto"
                >
                    <Tab label="Thống kê cộng đồng" />
                    <Tab label="Hoạt động người dùng" />
                    <Tab label="Nội dung phổ biến" />
                    <Tab label="Phân tích tìm kiếm" />
                </Tabs>

                {/* Tab Panel 0: Community Stats */}
                <TabPanel value={selectedTab} index={0}>
                    {communityData && (
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={4}>
                                <Paper sx={{ p: 3, height: '100%' }}>
                                    <Typography variant="h6" gutterBottom>Thống kê bài viết</Typography>
                                    <Typography>Tổng số bài viết: {communityData.posts.totalPosts}</Typography>
                                    <Typography>Bài viết nổi bật: {communityData.posts.featuredPosts}</Typography>
                                    <Typography>Tổng lượt xem: {communityData.posts.totalViews}</Typography>
                                    <Typography>Tổng bình luận: {communityData.posts.totalComments}</Typography>
                                </Paper>
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <Paper sx={{ p: 3, height: '100%' }}>
                                    <Typography variant="h6" gutterBottom>Thống kê chủ đề</Typography>
                                    <Typography>Tổng số chủ đề: {communityData.topics.totalTopics}</Typography>
                                    <Typography>Chủ đề nổi bật: {communityData.topics.featuredTopics}</Typography>
                                </Paper>
                            </Grid>
                            <Grid item xs={12} md={4}>
                                <Paper sx={{ p: 3, height: '100%' }}>
                                    <Typography variant="h6" gutterBottom>Thống kê đánh giá</Typography>
                                    <Typography>Tổng số đánh giá: {communityData.ratings.stats.totalRatings}</Typography>
                                    <Typography>Đánh giá trung bình: {communityData.ratings.stats.averageRating.toFixed(2)} <StarIcon sx={{ color: 'gold', verticalAlign: 'middle' }} /></Typography>
                                </Paper>
                            </Grid>
                            <Grid item xs={12}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>Đánh giá gần đây</Typography>
                                    <List>
                                        {communityData.ratings.latestRatings.map((rating, index) => (
                                            <React.Fragment key={rating._id}>
                                                <ListItem>
                                                    <ListItemAvatar>
                                                        <Avatar>{rating.userId.fullName.charAt(0)}</Avatar>
                                                    </ListItemAvatar>
                                                    <ListItemText
                                                        primary={`${rating.userId.fullName} đã đánh giá bài viết "${rating.postId.title}"`}
                                                        secondary={`Điểm: ${rating.rating}/5 - ${formatDate(rating.createdAt)}`}
                                                    />
                                                </ListItem>
                                                {index < communityData.ratings.latestRatings.length - 1 && <Divider />}
                                            </React.Fragment>
                                        ))}
                                    </List>
                                </Paper>
                            </Grid>
                        </Grid>
                    )}
                </TabPanel>


                {/* Tab Panel 1: User Activity */}
                <TabPanel value={selectedTab} index={1}>
                    {userActivityData && (
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={4}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Loại hoạt động
                                    </Typography>
                                    <Box sx={{ height: 300, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                        <Doughnut
                                            data={{
                                                labels: userActivityData.activityStats.map(item => item._id),
                                                datasets: [{
                                                    data: userActivityData.activityStats.map(item => item.count),
                                                    backgroundColor: [
                                                        '#FF6384',
                                                        '#36A2EB',
                                                        '#FFCE56',
                                                        '#4BC0C0',
                                                        '#9966FF',
                                                        '#FF9F40'
                                                    ],
                                                    borderWidth: 2,
                                                    borderColor: '#fff',
                                                    hoverBorderWidth: 3,
                                                    hoverBorderColor: '#fff'
                                                }]
                                            }}
                                            options={{
                                                responsive: true,
                                                maintainAspectRatio: false,
                                                plugins: {
                                                    legend: {
                                                        position: 'bottom',
                                                        labels: {
                                                            padding: 20,
                                                            usePointStyle: true
                                                        }
                                                    },
                                                    tooltip: {
                                                        callbacks: {
                                                            label: function (context) {
                                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                                                            }
                                                        }
                                                    }
                                                }
                                            }}
                                        />
                                    </Box>
                                </Paper>
                            </Grid>

                            <Grid item xs={12} md={8}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Hoạt động theo ngày
                                    </Typography>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <AreaChart data={userActivityData.dailyActivity}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis
                                                dataKey="date"
                                                tickFormatter={formatDate}
                                            />
                                            <YAxis />
                                            <Tooltip
                                                labelFormatter={(value) => formatDate(value)}
                                            />
                                            <Area
                                                type="monotone"
                                                dataKey="count"
                                                stackId="1"
                                                stroke="#8884d8"
                                                fill="#8884d8"
                                                name="Tổng hoạt động"
                                            />
                                            <Area
                                                type="monotone"
                                                dataKey="uniqueUserCount"
                                                stackId="2"
                                                stroke="#82ca9d"
                                                fill="#82ca9d"
                                                name="Người dùng duy nhất"
                                            />
                                        </AreaChart>
                                    </ResponsiveContainer>
                                </Paper>
                            </Grid>

                            <Grid item xs={12}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Top người dùng hoạt động nhất
                                    </Typography>
                                    <List>
                                        {userActivityData.topActiveUsers.slice(0, 10).map((user, index) => (
                                            <React.Fragment key={user._id._id}>
                                                <ListItem>
                                                    <ListItemAvatar>
                                                        <Avatar src={user._id.avatarUrl}>
                                                            {user._id.fullName?.charAt(0)}
                                                        </Avatar>
                                                    </ListItemAvatar>
                                                    <ListItemText
                                                        primary={user._id.fullName || 'Người dùng ẩn danh'}
                                                        secondary={`${user.activityCount} hoạt động`}
                                                    />
                                                    <Chip
                                                        label={`#${index + 1}`}
                                                        size="small"
                                                        color="primary"
                                                    />
                                                </ListItem>
                                                {index < 9 && <Divider />}
                                            </React.Fragment>
                                        ))}
                                    </List>
                                </Paper>
                            </Grid>
                        </Grid>
                    )}
                </TabPanel>

                {/* Tab Panel 2: Popular Content */}
                <TabPanel value={selectedTab} index={2}>
                    {popularContentData && (
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={6}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Bài viết phổ biến nhất
                                    </Typography>
                                    <List>
                                        {popularContentData.popularPosts.slice(0, 10).map((post, index) => (
                                            <React.Fragment key={post._id._id}>
                                                <ListItem>
                                                    <ListItemText
                                                        primary={post._id.title}
                                                        secondary={
                                                            <Box>
                                                                <Typography variant="caption" display="block">
                                                                    Tác giả: {post._id.authorId?.fullName || 'Ẩn danh'}
                                                                </Typography>
                                                                <Typography variant="caption" display="block">
                                                                    Chủ đề: {post._id.topicId?.name || 'Không xác định'}
                                                                </Typography>
                                                                <Box sx={{ mt: 1 }}>
                                                                    <Chip label={`${post.viewCount} lượt xem`} size="small" sx={{ mr: 1 }} />
                                                                    <Chip label={`${post.likeCount} thích`} size="small" color="error" sx={{ mr: 1 }} />
                                                                    <Chip label={`${post.commentCount} bình luận`} size="small" color="info" />
                                                                </Box>
                                                            </Box>
                                                        }
                                                    />
                                                    <Chip
                                                        label={`#${index + 1}`}
                                                        size="small"
                                                        color="primary"
                                                    />
                                                </ListItem>
                                                {index < 9 && <Divider />}
                                            </React.Fragment>
                                        ))}
                                    </List>
                                </Paper>
                            </Grid>

                            <Grid item xs={12} md={6}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Chủ đề phổ biến nhất
                                    </Typography>
                                    <List>
                                        {popularContentData.popularTopics.slice(0, 10).map((topic, index) => (
                                            <React.Fragment key={topic._id}>
                                                <ListItem>
                                                    <ListItemText
                                                        primary={topic.name}
                                                        secondary={
                                                            <Box>
                                                                <Typography variant="caption" display="block">
                                                                    Danh mục: {topic.category}
                                                                </Typography>
                                                                <Box sx={{ mt: 1 }}>
                                                                    <Chip label={`${topic.postCount} bài viết`} size="small" sx={{ mr: 1 }} />
                                                                    <Chip label={`${topic.viewCount} lượt xem`} size="small" color="info" sx={{ mr: 1 }} />
                                                                    <Chip label={`${topic.recentPostCount} bài mới`} size="small" color="success" />
                                                                </Box>
                                                            </Box>
                                                        }
                                                    />
                                                    <Chip
                                                        label={`#${index + 1}`}
                                                        size="small"
                                                        color="primary"
                                                    />
                                                </ListItem>
                                                {index < 9 && <Divider />}
                                            </React.Fragment>
                                        ))}
                                    </List>
                                </Paper>
                            </Grid>

                            <Grid item xs={12}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Thống kê theo danh mục
                                    </Typography>
                                    <ResponsiveContainer width="100%" height={400}>
                                        <BarChart data={popularContentData.categoryStats}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis dataKey="_id" />
                                            <YAxis />
                                            <Tooltip />
                                            <Legend />
                                            <Bar dataKey="topicCount" fill="#8884d8" name="Số chủ đề" />
                                            <Bar dataKey="totalPosts" fill="#82ca9d" name="Tổng bài viết" />
                                            <Bar dataKey="totalViews" fill="#ffc658" name="Tổng lượt xem" />
                                        </BarChart>
                                    </ResponsiveContainer>
                                </Paper>
                            </Grid>
                        </Grid>
                    )}
                </TabPanel>

                {/* Tab Panel 3: Search Analytics */}
                <TabPanel value={selectedTab} index={3}>
                    {searchAnalyticsData && (
                        <Grid container spacing={3}>
                            <Grid item xs={12} md={4}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Tổng quan tìm kiếm
                                    </Typography>
                                    <Box sx={{ mt: 2 }}>
                                        <Typography variant="h4" color="primary.main">
                                            {formatNumber(searchAnalyticsData.overview.totalSearches)}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" gutterBottom>
                                            Tổng số lượt tìm kiếm
                                        </Typography>

                                        <Typography variant="h4" color="info.main">
                                            {formatNumber(searchAnalyticsData.overview.uniqueQueries)}
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary" gutterBottom>
                                            Từ khóa duy nhất
                                        </Typography>

                                        <Typography variant="h4" color="success.main">
                                            {searchAnalyticsData.overview.avgProcessingTime.toFixed(0)}ms
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Thời gian xử lý trung bình
                                        </Typography>
                                    </Box>
                                </Paper>
                            </Grid>

                            <Grid item xs={12} md={8}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Xu hướng tìm kiếm theo ngày
                                    </Typography>
                                    <ResponsiveContainer width="100%" height={300}>
                                        <LineChart data={searchAnalyticsData.searchTrends}>
                                            <CartesianGrid strokeDasharray="3 3" />
                                            <XAxis
                                                dataKey="_id"
                                                tickFormatter={(value) => `${value.day}/${value.month}`}
                                            />
                                            <YAxis />
                                            <Tooltip />
                                            <Legend />
                                            <Line
                                                type="monotone"
                                                dataKey="searchCount"
                                                stroke="#8884d8"
                                                name="Số lượt tìm kiếm"
                                            />
                                            <Line
                                                type="monotone"
                                                dataKey="uniqueQueryCount"
                                                stroke="#82ca9d"
                                                name="Từ khóa duy nhất"
                                            />
                                        </LineChart>
                                    </ResponsiveContainer>
                                </Paper>
                            </Grid>

                            <Grid item xs={12} md={6}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Từ khóa tìm kiếm phổ biến
                                    </Typography>
                                    <List>
                                        {searchAnalyticsData.popularSearches.slice(0, 10).map((search, index) => (
                                            <React.Fragment key={search._id}>
                                                <ListItem>
                                                    <ListItemText
                                                        primary={search.originalQuery}
                                                        secondary={
                                                            <Box>
                                                                <Chip
                                                                    label={`${search.count} lượt`}
                                                                    size="small"
                                                                    sx={{ mr: 1 }}
                                                                />
                                                                <Chip
                                                                    label={`${(search.successRate * 100).toFixed(0)}% thành công`}
                                                                    size="small"
                                                                    color={search.successRate > 0.5 ? 'success' : 'warning'}
                                                                />
                                                            </Box>
                                                        }
                                                    />
                                                    <Chip
                                                        label={`#${index + 1}`}
                                                        size="small"
                                                        color="primary"
                                                    />
                                                </ListItem>
                                                {index < 9 && <Divider />}
                                            </React.Fragment>
                                        ))}
                                    </List>
                                </Paper>
                            </Grid>

                            <Grid item xs={12} md={6}>
                                <Paper sx={{ p: 3 }}>
                                    <Typography variant="h6" gutterBottom>
                                        Tìm kiếm thất bại
                                    </Typography>
                                    <List>
                                        {searchAnalyticsData.failedSearches.slice(0, 10).map((search, index) => (
                                            <React.Fragment key={search._id}>
                                                <ListItem>
                                                    <ListItemText
                                                        primary={search.originalQuery}
                                                        secondary={`${search.count} lần thất bại`}
                                                    />
                                                    <Chip
                                                        label={search.count}
                                                        size="small"
                                                        color="error"
                                                    />
                                                </ListItem>
                                                {index < 9 && <Divider />}
                                            </React.Fragment>
                                        ))}
                                    </List>
                                </Paper>
                            </Grid>
                        </Grid>
                    )}
                </TabPanel>
            </Paper>
        </Container>
    );
};

export default AdminAnalyticsPage;
