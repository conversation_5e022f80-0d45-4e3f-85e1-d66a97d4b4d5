{"version": 3, "sources": ["../../@mui/utils/esm/useLazyRef/useLazyRef.js", "../../@mui/utils/esm/useOnMount/useOnMount.js", "../../@mui/utils/esm/useTimeout/useTimeout.js", "../../@mui/utils/esm/refType/refType.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst UNINITIALIZED = {};\n\n/**\n * A React.useRef() that is initialized lazily with a function. Note that it accepts an optional\n * initialization argument, so the initialization function doesn't need to be an inline closure.\n *\n * @usage\n *   const ref = useLazyRef(sortColumns, columns)\n */\nexport default function useLazyRef(init, initArg) {\n  const ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = init(initArg);\n  }\n  return ref;\n}", "'use client';\n\nimport * as React from 'react';\nconst EMPTY = [];\n\n/**\n * A React.useEffect equivalent that runs once, when the component is mounted.\n */\nexport default function useOnMount(fn) {\n  // TODO: uncomment once we enable eslint-plugin-react-compiler // eslint-disable-next-line react-compiler/react-compiler -- no need to put `fn` in the dependency array\n  /* eslint-disable react-hooks/exhaustive-deps */\n  React.useEffect(fn, EMPTY);\n  /* eslint-enable react-hooks/exhaustive-deps */\n}", "'use client';\n\nimport useLazyRef from \"../useLazyRef/useLazyRef.js\";\nimport useOnMount from \"../useOnMount/useOnMount.js\";\nexport class Timeout {\n  static create() {\n    return new Timeout();\n  }\n  currentId = null;\n\n  /**\n   * Executes `fn` after `delay`, clearing any previously scheduled call.\n   */\n  start(delay, fn) {\n    this.clear();\n    this.currentId = setTimeout(() => {\n      this.currentId = null;\n      fn();\n    }, delay);\n  }\n  clear = () => {\n    if (this.currentId !== null) {\n      clearTimeout(this.currentId);\n      this.currentId = null;\n    }\n  };\n  disposeEffect = () => {\n    return this.clear;\n  };\n}\nexport default function useTimeout() {\n  const timeout = useLazyRef(Timeout.create).current;\n  useOnMount(timeout.disposeEffect);\n  return timeout;\n}", "import PropTypes from 'prop-types';\nconst refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);\nexport default refType;"], "mappings": ";;;;;;;;;;;;AAEA,YAAuB;AACvB,IAAM,gBAAgB,CAAC;AASR,SAAR,WAA4B,MAAM,SAAS;AAChD,QAAM,MAAY,aAAO,aAAa;AACtC,MAAI,IAAI,YAAY,eAAe;AACjC,QAAI,UAAU,KAAK,OAAO;AAAA,EAC5B;AACA,SAAO;AACT;;;AChBA,IAAAA,SAAuB;AACvB,IAAM,QAAQ,CAAC;AAKA,SAAR,WAA4B,IAAI;AAGrC,EAAM,iBAAU,IAAI,KAAK;AAE3B;;;ACTO,IAAM,UAAN,MAAM,SAAQ;AAAA,EAAd;AAIL,qCAAY;AAYZ,iCAAQ,MAAM;AACZ,UAAI,KAAK,cAAc,MAAM;AAC3B,qBAAa,KAAK,SAAS;AAC3B,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,yCAAgB,MAAM;AACpB,aAAO,KAAK;AAAA,IACd;AAAA;AAAA,EAvBA,OAAO,SAAS;AACd,WAAO,IAAI,SAAQ;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,OAAO,IAAI;AACf,SAAK,MAAM;AACX,SAAK,YAAY,WAAW,MAAM;AAChC,WAAK,YAAY;AACjB,SAAG;AAAA,IACL,GAAG,KAAK;AAAA,EACV;AAUF;AACe,SAAR,aAA8B;AACnC,QAAM,UAAU,WAAW,QAAQ,MAAM,EAAE;AAC3C,aAAW,QAAQ,aAAa;AAChC,SAAO;AACT;;;AClCA,wBAAsB;AACtB,IAAM,UAAU,kBAAAC,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACtE,IAAO,kBAAQ;", "names": ["React", "PropTypes"]}