{"version": 3, "sources": ["../../@mui/icons-material/esm/Category.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"m12 2-5.5 9h11z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"17.5\",\n  cy: \"17.5\",\n  r: \"4.5\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M3 13.5h8v8H3z\"\n}, \"2\")], 'Category');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,mBAAQ,cAAc,KAAc,mBAAAA,KAAK,QAAQ;AAAA,EACtD,GAAG;AACL,GAAG,GAAG,OAAgB,mBAAAA,KAAK,UAAU;AAAA,EACnC,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,GAAG;AACL,GAAG,GAAG,OAAgB,mBAAAA,KAAK,QAAQ;AAAA,EACjC,GAAG;AACL,GAAG,GAAG,CAAC,GAAG,UAAU;", "names": ["_jsx"]}