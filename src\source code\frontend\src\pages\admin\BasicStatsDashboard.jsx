import React, { useState, useEffect } from 'react';
import {
    Typo<PERSON>,
    Grid,
    Paper,
    Box,
    Card,
    CardContent,
    CircularProgress,
    Alert,
    Chip,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Button
} from '@mui/material';
import {
    People as PeopleIcon,
    Article as ArticleIcon,
    Comment as CommentIcon,
    Favorite as FavoriteIcon,
    Star as StarIcon,
    Reply as ReplyIcon,
    TrendingUp as TrendingUpIcon,
    Refresh as RefreshIcon,
    Topic as TopicIcon,
    HowToVote as HowToVoteIcon
} from '@mui/icons-material';
import {
    LineChart,
    Line,
    AreaChart,
    Area,
    PieChart,
    Pie,
    Cell,
    XAxis,
    YAxis,
    CartesianGrid,
    Tooltip,
    Legend,
    ResponsiveContainer
} from 'recharts';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    Tooltip as ChartTooltip,
    Legend as ChartLegend,
    ArcElement,
    BarElement
} from 'chart.js';
import { Doughnut, PolarArea } from 'react-chartjs-2';
import axios from 'axios';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    Title,
    ChartTooltip,
    ChartLegend,
    ArcElement,
    BarElement
);

const BasicStatsDashboard = () => {
    // State management
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [basicStats, setBasicStats] = useState(null);
    const [timePeriod, setTimePeriod] = useState('last_month');

    // API base URL
    const API_BASE_URL = 'http://localhost:5000/api';

    // Colors for charts
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

    // Get auth token
    const getAuthToken = () => {
        const token = localStorage.getItem('token');
        if (token) return token;
        const user = JSON.parse(localStorage.getItem('user') || '{}');
        return user.token;
    };

    // Format number with K, M suffix
    const formatNumber = (num) => {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    };

    // Fetch basic statistics
    const fetchBasicStats = async () => {
        try {
            setLoading(true);
            setError(null);

            const token = getAuthToken();
            const headers = {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            };

            const [overviewResponse, communityStatsResponse] = await Promise.all([
                axios.get(`${API_BASE_URL}/admin/analytics/overview`, { headers }),
                axios.get(`${API_BASE_URL}/admin/analytics/community?period=${timePeriod}`, { headers }),
            ]);

            const overviewData = overviewResponse.data.data;
            const communityData = communityStatsResponse.data.data;

            const realData = {
                totals: {
                    users: overviewData.totals.users || 0,
                    posts: overviewData.totals.posts || 0,
                    comments: overviewData.totals.comments || 0,
                    likes: overviewData.totals.likes || 0,
                    ratings: communityData.ratings.stats.totalRatings || 0,
                    totalTopics: communityData.topics.totalTopics || 0,
                    featuredTopics: communityData.topics.featuredTopics || 0,
                    featuredPosts: communityData.posts.featuredPosts || 0,
                    uniqueRaters: communityData.ratings.latestRatings.length || 0,
                },
                contentDistribution: [
                    { name: 'Bài viết', value: overviewData.totals.posts || 0, color: '#0088FE' },
                    { name: 'Bình luận', value: overviewData.totals.comments || 0, color: '#00C49F' },
                    { name: 'Chủ đề', value: communityData.topics.totalTopics || 0, color: '#FFBB28' },
                    { name: 'Người dùng', value: overviewData.totals.users || 0, color: '#FF8042' }
                ],
                engagementStats: [
                    { name: 'Lượt thích', value: overviewData.totals.likes || 0, color: '#FF6384' },
                    { name: 'Bình luận', value: overviewData.totals.comments || 0, color: '#36A2EB' },
                    { name: 'Đánh giá', value: communityData.ratings.stats.totalRatings || 0, color: '#FFCE56' },
                ]
            };

            setBasicStats(realData);
        } catch (error) {
            console.error('Error fetching basic stats:', error);
            setError('Lỗi khi tải dữ liệu thống kê');
        } finally {
            setLoading(false);
        }
    };

    // Effects
    useEffect(() => {
        fetchBasicStats();
    }, [timePeriod]);

    if (loading) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
                <CircularProgress />
            </Box>
        );
    }

    return (
        <>
            <Typography variant="h4" gutterBottom>
                📊 Thống Kê Cơ Bản
            </Typography>

            <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 3, gap: 2 }}>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                    <InputLabel>Thời gian</InputLabel>
                    <Select
                        value={timePeriod}
                        label="Thời gian"
                        onChange={(e) => setTimePeriod(e.target.value)}
                    >
                        <MenuItem value={'yesterday'}>Hôm qua</MenuItem>
                        <MenuItem value={'last_week'}>Tuần trước</MenuItem>
                        <MenuItem value={'last_month'}>Tháng trước</MenuItem>
                    </Select>
                </FormControl>
                <Button
                    variant="outlined"
                    startIcon={<RefreshIcon />}
                    onClick={fetchBasicStats}
                    disabled={loading}
                >
                    Làm mới
                </Button>
            </Box>

            {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                </Alert>
            )}

            {/* Overview Stats Cards */}
            {basicStats && (
                <>
                    <Grid container spacing={3} sx={{ mb: 4 }}>
                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <PeopleIcon sx={{ mr: 2, color: 'primary.main', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Người dùng
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.users)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <ArticleIcon sx={{ mr: 2, color: 'info.main', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Bài viết
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.posts)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <CommentIcon sx={{ mr: 2, color: 'warning.main', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Bình luận
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.comments)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <FavoriteIcon sx={{ mr: 2, color: 'error.main', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Lượt thích
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.likes)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <StarIcon sx={{ mr: 2, color: 'success.main', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Đánh giá
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.ratings)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <TopicIcon sx={{ mr: 2, color: 'primary.dark', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Tổng chủ đề
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.totalTopics)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <StarIcon sx={{ mr: 2, color: 'warning.dark', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Chủ đề nổi bật
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.featuredTopics)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <ArticleIcon sx={{ mr: 2, color: 'info.dark', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Bài viết nổi bật
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.featuredPosts)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={1.5}>
                            <Card>
                                <CardContent>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        <HowToVoteIcon sx={{ mr: 2, color: 'secondary.dark', fontSize: 40 }} />
                                        <Box>
                                            <Typography color="textSecondary" gutterBottom>
                                                Người đánh giá
                                            </Typography>
                                            <Typography variant="h5">
                                                {formatNumber(basicStats.totals.uniqueRaters)}
                                            </Typography>
                                        </Box>
                                    </Box>
                                </CardContent>
                            </Card>
                        </Grid>
                    </Grid>

                    {/* Charts Section */}
                    <Grid container spacing={3}>
                        {/* Content Distribution Pie Chart */}
                        <Grid item xs={12} md={6}>
                            <Paper sx={{ p: 3 }}>
                                <Typography variant="h6" gutterBottom>
                                    🥧 Phân bố nội dung
                                </Typography>
                                <ResponsiveContainer width="100%" height={350}>
                                    <PieChart>
                                        <Pie
                                            data={basicStats.contentDistribution}
                                            cx="50%"
                                            cy="50%"
                                            labelLine={false}
                                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                            outerRadius={100}
                                            fill="#8884d8"
                                            dataKey="value"
                                        >
                                            {basicStats.contentDistribution.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                        <Tooltip />
                                        <Legend />
                                    </PieChart>
                                </ResponsiveContainer>
                            </Paper>
                        </Grid>

                        {/* Engagement Stats Doughnut Chart */}
                        <Grid item xs={12} md={6}>
                            <Paper sx={{ p: 3 }}>
                                <Typography variant="h6" gutterBottom>
                                    🍩 Thống kê tương tác
                                </Typography>
                                <Box sx={{ height: 350, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                    <Doughnut
                                        data={{
                                            labels: basicStats.engagementStats.map(item => item.name),
                                            datasets: [{
                                                data: basicStats.engagementStats.map(item => item.value),
                                                backgroundColor: basicStats.engagementStats.map(item => item.color),
                                                borderWidth: 2,
                                                borderColor: '#fff',
                                                hoverBorderWidth: 3,
                                                hoverBorderColor: '#fff'
                                            }]
                                        }}
                                        options={{
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            plugins: {
                                                legend: {
                                                    position: 'bottom',
                                                    labels: {
                                                        padding: 20,
                                                        usePointStyle: true
                                                    }
                                                },
                                                tooltip: {
                                                    callbacks: {
                                                        label: function (context) {
                                                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                                                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                                                        }
                                                    }
                                                }
                                            }
                                        }}
                                    />
                                </Box>
                            </Paper>
                        </Grid>
                    </Grid>
                </>
            )}
        </>
    );
};

export default BasicStatsDashboard;
